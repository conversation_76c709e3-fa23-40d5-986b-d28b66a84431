const withPlugins       = require('next-compose-plugins');
const { withContentlayer } = require('next-contentlayer');
const withMDX           = require('@next/mdx')({ extension: /\.mdx?$/ });

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
    async redirects() {
      return [
        {
          source: '/etude-cas-projets',
          destination: '/',
          permanent: false, // return a 308 code
          },
          {
            source: '/odoo-web-site',
            destination: '/',
            permanent: false, // return a 308 code
          },
          {
            source: '/agence',
            destination: '/',
            permanent: false, // return a 308 code
          },
          {
            source: '/audit-de-site-web-gratuit',
            destination: '/',
            permanent: false, // return a 308 code
          },
          {
            source: '/subvention-pcan',
            destination: '/',
            permanent: false, // return a 308 code
          },
          {
            source: '/mentions-legales',
            destination: '/legal/termes-conditions',
            permanent: true, // return a 308 code
          },
          {
            source: '/politique-confidentialite',
            destination: '/legal/cookies',
            permanent: true, // return a 308 code
          },
          {
            source: '/portfolio/oca-odoo-community-association',
            destination: '/projets/identite-odoo-community-association',
            permanent: true, // return a 308 code
          },
          {
            source: '/portfolio/cap-financimmo',
            destination: '/projets/logo-cap-financimmo',
            permanent: true, // return a 308 code
          },
          {
            source: '/portfolio/copilote-identite-visuelle',
            destination: '/projets/identite-copilote',
            permanent: true, // return a 308 code
          },
          {
            source: '/portfolio/kolabus-identite-marque-odoo',
            destination: '/projets/identite-kolabus',
            permanent: true, // return a 308 code
        },
        {
          source: '/index.php/portfolio/kolabus/',
          destination: '/projets/identite-kolabus',
          permanent: true, // return a 308 code
        },
        {
          source: '/portfolio/numigi-refonte-identite-visuelle/',
          destination: '/projets/site-web-numigi',
          permanent: true, // return a 308 code
        },
        {
          source: '/audit-site-web-gratuit/',
          destination: '/',
          permanent: true, // return a 308 code
        },
      ]
  },
  
  pageExtensions: ['js', 'jsx', 'ts', 'tsx', 'md', 'mdx'],

  }
  
module.exports = withPlugins([withMDX, withContentlayer], nextConfig);
  
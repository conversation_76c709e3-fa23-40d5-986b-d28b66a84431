.testimonials {
  padding: var(--section-padding) 0;
  background-color: #FCFCF8;
  position: relative;
  overflow: hidden;

  hr {
    margin-bottom: var(--gap-padding);
  }

h2 {
    max-width: 500px;
  }

  .separator {
    width: 100%;
    height: 1px;
    background-color: rgba(28, 29, 32, 0.176);
    margin-bottom: calc(var(--gap-padding) * 2);
  }

  .header {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--gap-padding);
    margin-bottom: calc(var(--gap-padding) * 3);
    align-items: start;

    .leftSection {
      .sectionLabel {
        font-size: 18px;
        font-weight: 400;
        color: rgba(28, 29, 32, 0.7);
        margin: 0;
        line-height: 1.4;
      }
    }

    .rightSection {
      .sectionTitle {
        font-size: clamp(32px, 4vw, 48px);
        font-weight: 400;
        line-height: 1.2;
        margin: 0 0 24px 0;
        color: #1c1d20;
      }

      .viewAllButton {
        margin-top: var(--gap-padding);
        width: fit-content;
      }
    }
  }

  .carouselSection {
    margin-top: calc(var(--gap-padding) * 2);
  }
}

// Responsive design
@media (max-width: 768px) {
  .testimonials {
    .header {
      grid-template-columns: 1fr;
      gap: calc(var(--gap-padding) / 2);
      margin-bottom: calc(var(--gap-padding) * 2);

      .leftSection {
        .sectionLabel {
          font-size: 16px;
          margin-bottom: 16px;
        }
      }

      .rightSection {
        .sectionTitle {
          font-size: clamp(24px, 6vw, 32px);
          margin-bottom: 16px;
        }
      }
    }

    .carouselSection {
      margin-top: var(--gap-padding);
    }
  }
}

@media (max-width: 480px) {
  .testimonials {
    .header {
      .rightSection {
        .sectionTitle {
          font-size: 24px;
        }
      }
    }
  }
}

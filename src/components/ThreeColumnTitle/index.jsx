import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';

export default function ThreeColumnTitle({ 
  locale = 'fr',
  titleKey = 'agency.our_vision.title',
  descriptionKey = 'agency.our_vision.description',
  namespace = 'pages'
}) {
  const { t } = useTranslation(namespace);

  return (
    <section className={`${styles.threeColumnTitle} section`}>
      <div className="container">
        <div className={styles.grid}>
          {/* Colonne gauche - Titre + Image */}
          <div className={styles.leftColumn}>
            <h2 className={styles.title}>
              {t(titleKey)}
            </h2>
            <div className={styles.imageWrapper}>
              <Image
                src="https://via.placeholder.com/400x400/CCCCCC/666666?text=Image+1"
                alt={t(titleKey)}
                width={400}
                height={400}
                className={styles.image}
              />
            </div>
          </div>

          {/* Colonne du milieu - Grande image */}
          <div className={styles.centerColumn}>
            <div className={styles.imageWrapper}>
              <Image
                src="https://via.placeholder.com/600x400/CCCCCC/666666?text=Image+principale"
                alt="Image principale"
                width={600}
                height={400}
                className={styles.image}
              />
            </div>
          </div>

          {/* Colonne droite - Image + Description */}
          <div className={styles.rightColumn}>
            <div className={styles.imageWrapper}>
              <Image
                src="https://via.placeholder.com/400x400/CCCCCC/666666?text=Image+2"
                alt="Image secondaire"
                width={400}
                height={400}
                className={styles.image}
              />
            </div>
            <p className={styles.description}>
              {t(descriptionKey)}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

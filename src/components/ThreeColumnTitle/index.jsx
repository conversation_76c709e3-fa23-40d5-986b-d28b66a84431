import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';

export default function ThreeColumnTitle({ 
  locale = 'fr',
  titleKey = 'agency.our_vision.title',
  descriptionKey = 'agency.our_vision.description',
  namespace = 'pages'
}) {
  const { t } = useTranslation(namespace);

  return (
    <section className={`${styles.threeColumnTitle} section`}>
      <div className="container">
        <div className={styles.grid}>
          {/* Colonne 1 - Titre + Image */}
          <div className={styles.column}>
            <h2 className={styles.title}>
              {t(titleKey)}
            </h2>
            <div className={styles.imageWrapper}>
              <Image
                src="https://placehold.co/400x400"
                alt={t(titleKey)}
                width={400}
                height={400}
                className={styles.image}
              />
            </div>
          </div>

          {/* Colonne 2 - Grande image */}
          <div className={styles.column}>
            <div className={styles.imageWrapper}>
              <Image
                src="https://placehold.co/600x400"
                alt="Image principale"
                width={600}
                height={400}
                className={styles.image}
              />
            </div>
          </div>

          {/* Colonne 3 - Image + Description */}
          <div className={styles.column}>
            <div className={styles.imageWrapper}>
              <Image
                src="https://placehold.co/400x400"
                alt="Image secondaire"
                width={400}
                height={400}
                className={styles.image}
              />
            </div>
            <p className={styles.description}>
              {t(descriptionKey)}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
